/**
 * ========================================================================
 * MOVISTAR API SERVICE
 * ========================================================================
 *
 * Servicio para realizar búsquedas de contenido en la API de Movistar Plus
 * Maneja peticiones GET a la API de búsqueda de contenidos
 * ========================================================================
 */

import axios from 'axios';
import { handleRequest } from './_helpersService';
import type { MovistarContent, MovistarSearchOptions, MovistarAPIResponse } from '../models/services';

// ========== CONFIGURACIÓN ==========
export const MOVISTAR_CONFIG = {
  BASE_URL: import.meta.env.VITE_MOVISTAR_API_URL || 'https://ottcache.dof6.com/movistarplus/remote/contents/search?genre=Cine&filter=BU-TITULO&mode=VODREJILLA&phrase=',
  REQUEST_TIMEOUT: 10000, // 10 segundos
  RETRY_ATTEMPTS: 2,
  RETRY_DELAY: 1000, // 1 segundo
} as const;

// ========== TIPOS INTERNOS ==========
// Usando MovistarAPIResponse importado desde models/services.ts

// ========== CLASE PRINCIPAL ==========
export class MovistarAPIService {
  private serviceName = 'MovistarAPIService';

  // ========== MÉTODO PRINCIPAL DE BÚSQUEDA ==========
  /**
   * Busca contenido por personaje en la API de Movistar Plus
   * @param character Nombre del personaje a buscar
   * @param options Opciones adicionales de búsqueda
   * @returns Primer resultado encontrado o null si no hay resultados
   */
  public async searchByCharacter(
    character: string,
    options: MovistarSearchOptions = {}
  ): Promise<MovistarContent | null> {
    if (!character || character.trim().length === 0) {
      console.warn(`⚠️ [${this.serviceName}] Personaje vacío proporcionado`);
      return null;
    }

    const cleanCharacter = character.trim();
    console.log(`🔍 [${this.serviceName}] Buscando contenido para: "${cleanCharacter}"`);

    try {
      const response = await this.makeSearchRequest(cleanCharacter, options);
      const firstResult = this.extractFirstResult(response);

      if (firstResult) {
        console.log(`✅ [${this.serviceName}] Contenido encontrado:`, firstResult.title);
        return firstResult;
      } else {
        console.log(`ℹ️ [${this.serviceName}] No se encontró contenido para: "${cleanCharacter}"`);
        return null;
      }
    } catch (error) {
      console.error(`❌ [${this.serviceName}] Error buscando contenido:`, error);
      return null;
    }
  }

  // ========== MÉTODOS PRIVADOS ==========
  /**
   * Realiza la petición HTTP a la API de Movistar
   */
  private async makeSearchRequest(
    character: string,
    options: MovistarSearchOptions
  ): Promise<MovistarAPIResponse> {
    const url = this.buildSearchURL(character, options);

    console.log(`🌐 [${this.serviceName}] URL de búsqueda: ${url}`);

    const response = await handleRequest<MovistarAPIResponse>(
      axios.get(url, {
        timeout: MOVISTAR_CONFIG.REQUEST_TIMEOUT,
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'Genigma-WebView/1.0',
        },
      })
    );

    return response;
  }

  /**
   * Construye la URL de búsqueda con los parámetros
   */
  private buildSearchURL(character: string, _options: MovistarSearchOptions): string {
    const encodedCharacter = encodeURIComponent(character);
    return `${MOVISTAR_CONFIG.BASE_URL}${encodedCharacter}`;
  }

  /**
   * Extrae el primer resultado de la respuesta de la API
   */
  private extractFirstResult(response: MovistarAPIResponse): MovistarContent | null {
    // Primero intentamos obtener contenido de la sección "Contenidos"
    if (response.Contenidos && Array.isArray(response.Contenidos) && response.Contenidos.length > 0) {
      const firstContent = response.Contenidos[0];
      if (firstContent.DatosEditoriales) {
        return this.mapFromDatosEditoriales(firstContent.DatosEditoriales);
      }
    }

    // Si no hay contenidos, intentamos usar los tags como fallback
    if (response.tags && Array.isArray(response.tags) && response.tags.length > 0) {
      // Buscar tags de tipo "1" (contenido) o "3" (personajes)
      const contentTags = response.tags.filter(tag => tag.type === "1" || tag.type === "3");
      if (contentTags.length > 0) {
        return this.mapFromTag(contentTags[0]);
      }
    }

    return null;
  }

  /**
   * Mapea desde DatosEditoriales a MovistarContent
   */
  private mapFromDatosEditoriales(datos: any): MovistarContent {
    return {
      id: datos.Id,
      title: datos.Titulo || 'Título desconocido',
      description: datos.Descripcion,
      image: datos.Imagen,
      type: 'content', // Los contenidos son de tipo content
      genre: datos.Genero,
      year: datos.Año,
      duration: datos.Duracion,
      rating: datos.Clasificacion,
      lanzable: datos.Lanzable,
      url: undefined, // No disponible en DatosEditoriales
    };
  }

  /**
   * Mapea desde Tag a MovistarContent (fallback)
   */
  private mapFromTag(tag: any): MovistarContent {
    // Buscar imagen en los links
    const imageLink = tag.links?.find((link: any) => link.type === 'image/jpg');

    return {
      id: parseInt(tag.id) || 0,
      title: tag.name || 'Título desconocido',
      description: undefined,
      image: imageLink?.href,
      type: tag.type === "1" ? 'content' : tag.type === "2" ? 'person' : tag.type === "3" ? 'character' : 'unknown',
      genre: undefined,
      year: undefined,
      duration: undefined,
      rating: undefined,
      lanzable: undefined,
      url: tag.links?.[0]?.href, // Primer link como URL principal
    };
  }

  // ========== MÉTODOS DE UTILIDAD ==========
  /**
   * Verifica si el servicio está configurado correctamente
   */
  public isConfigured(): boolean {
    return Boolean(MOVISTAR_CONFIG.BASE_URL);
  }

  /**
   * Obtiene la configuración actual del servicio
   */
  public getConfig() {
    return {
      serviceName: this.serviceName,
      baseUrl: MOVISTAR_CONFIG.BASE_URL,
      timeout: MOVISTAR_CONFIG.REQUEST_TIMEOUT,
      retryAttempts: MOVISTAR_CONFIG.RETRY_ATTEMPTS,
    };
  }

  /**
   * Realiza una búsqueda de prueba para verificar conectividad
   */
  public async healthCheck(): Promise<boolean> {
    try {
      // Usamos un término de búsqueda genérico para la prueba
      await this.searchByCharacter('test');
      return true;
    } catch {
      return false;
    }
  }
}

// ========== SINGLETON EXPORT ==========
export const movistarAPIService = new MovistarAPIService();
