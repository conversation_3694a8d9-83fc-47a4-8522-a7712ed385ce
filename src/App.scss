.loader-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgb(3, 47, 70);
}

.logs {
  position: absolute;
  bottom: 10px;
  right: 10px;
  max-width: 300px;
  max-height: 400px;
  outline: 1px solid;
  overflow: hidden;

  div {
    overflow-y: scroll;
    height: 100%;
  }
}

#welcome-screen-overlay {
  position: fixed;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 20, 40, 1);
  z-index: 9998;
  display: flex;
  justify-content: center;
  align-items: center;

  .welcome-screen {
    max-width: 600px;
    animation: mysticalAppear 1s ease-out forwards;

    h1 {
      font-size: clamp(28px, 5vw, 48px);
      font-weight: 600;
      color: #88FFD5;
      margin-bottom: 40px;
      line-height: 1.3;
      text-shadow: 0 0 20px rgba(136, 255, 213, 0.5);
    }

    p {
      font-size: clamp(18px, 3vw, 24px);
      color: #e0e0e0;
      margin-bottom: 60px;
      line-height: 1.6;
      max-width: 500px;
      margin: 0 auto 60px auto;
      opacity: 0.9;
      text-align: center;
    }

    .disclaimer-text {
      margin-top: 20px;
      font-size: 14px;
      color: #94a3b8;
      opacity: 0.8;
      animation: fadeIn 0.5s ease-in;
    }
  }
}

@keyframes mysticalAppear {
  from {
    opacity: 0;
    transform: scale(0.9) rotateY(10deg);
  }
  to {
    opacity: 1;
    transform: scale(1) rotateY(0deg);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 0.8; }
}

.game-container {
  position: relative;

  .background {
    width: 100vw;
    height: 100dvh;
    object-fit: cover;
    position: absolute;
  }

  .board {
    position: relative;
    height: 100dvh;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .content {
      flex: 1;
      display: flex;
      margin: 0 58px 48px;

      .menu-left {
        flex-basis: 183px;
        flex-shrink: 0;
        flex-grow: 0;
        align-self: end;

        div {
          text-align: center;
          font-weight: 700;
          font-style: Bold;
          font-size: 22px;
          line-height: 24px;
          letter-spacing: 0%;
          text-align: center;
          vertical-align: middle;
        }

        img {
          width: 121px;
          height: 121px;
          margin: 0 auto;
        }
      }    
    }
  }
}


// General buttons
.primary-button {
  box-shadow: 0px 0px 12px 0px #88ffd5;
  background-color: #88FFD5;
  border-radius: 0.5rem;
  color: black;
}


// Modal
.modal-fullscreen-wrapper {
  position: absolute;
  z-index: 20;

  .modal-wrapper {
    border: 2px solid #00d4aa;
    border-radius: 16px;
    border: 2px solid #88ffd5;
    box-shadow: 0px 0px 12px 0px #88ffd5;
    animation: popupSlideIn 0.3s ease-out;

    .modal-title {
      color: white;
    }

    .body1 {
      color: #bfc1c3;
    }

    .button-secondary {
      background-color: transparent;
      border: 1px solid #FFFFFF;
    }

    .button-primary {
      background: #88FFD5;
      color: black;
      border: none;
    }
  }
}

@keyframes popupSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}