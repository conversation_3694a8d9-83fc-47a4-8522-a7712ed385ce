import { useEffect, useState } from "react";
import { ControlButton, NavButton, SafeAreaHome } from "microapps";
import { useSpeechOutput } from "../../contexts/SpeechProvider";
import "./Header.scss";

interface HeaderProps {
  currentView: string;
  onBackToMain?: () => void;
  showBackButton?: boolean;
}

export const Header: React.FC<HeaderProps> = ({
  currentView,
  onBackToMain,
  showBackButton = false,
}) => {
  const {
    state: { audioState, isMusicPlaying, isAzureTTSEnabled: azureFromState},
    pauseMusic,
    resumeMusic,
    toggleAzureTTS,
  } = useSpeechOutput();
  const [currentAzureTTSState, setCurrentAzureTTSState] = useState(azureFromState);

  useEffect(() => {
    setCurrentAzureTTSState(azureFromState);
  }, [azureFromState]);

  const renderTitle = () => {
    switch (currentView) {
      case "play":
        return "Enygma";
      case "rules":
        return "Reglas";
      case "lives":
        return "Tus preguntas restantes";
      case "clues":
        return "Pistas descubiertas";
      default:
        return "";
    }
  };

  const handleMusicClick = () => {
    if (isMusicPlaying) {
      pauseMusic();
    } else {
      resumeMusic();
    }
  };

  const handleSpeechClick = () => {
    const newState = toggleAzureTTS();
    setCurrentAzureTTSState(newState);
  };

  return (
    <div className="header">
      <div className="header-left">
        {currentView === "main" &&
          <NavButton
            type="menu"
            onClick={() => {}}
            size="big"
          />
        }

        {showBackButton && currentView !== "main" && onBackToMain && (
          <NavButton
            type="back"
            onClick={onBackToMain}
            size="big"
          />
        )}
      </div>

      <div className="header-title">{renderTitle()}</div>

      <div className="header-right">
        <ControlButton
          onClick={handleMusicClick}
          type="music"
          isActive={ !audioState.isMuted && isMusicPlaying}
          size="big"
        />

        <ControlButton
          type="sound"
          isActive={currentAzureTTSState}
          size="big"
          onClick={handleSpeechClick}
          className={`speech-control`}
        />

        <div className="home-icon">
          <SafeAreaHome isVisible={false} />
        </div>
      </div>
    </div>
  );
};
