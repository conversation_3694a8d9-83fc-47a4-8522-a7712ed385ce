import { useState, useEffect, useCallback } from "react";
import { useSpeechOutput } from "../../contexts/SpeechProvider";
import { Modal } from "microapps";

interface CookieConsentBannerProps {
  onAudioActivated?: () => void;
  onConsentGiven?: () => void;
}

// Custom hook para manejar localStorage
const useCookieStorage = () => {
  const getItem = (key: string): string | null => {
    try {
      return localStorage.getItem(key);
    } catch {
      return null;
    }
  };

  const setItem = (key: string, value: string): void => {
    try {
      localStorage.setItem(key, value);
    } catch (error) {
      console.warn(`No se pudo guardar ${key} en localStorage:`, error);
    }
  };

  return { getItem, setItem };
};

// Constantes para evitar magic strings
const STORAGE_KEYS = {
  ANALYTICS_CONSENT: "enygma_analytics_consent",
  ANALYTICS_TIMESTAMP: "enygma_analytics_timestamp",
  AUDIO_ACTIVATED: "enygma_audio_activated",
} as const;

const CONSENT_VALUES = {
  ACCEPTED: "accepted",
  REJECTED: "rejected",
} as const;

export const CookieConsentBanner: React.FC<CookieConsentBannerProps> = ({
  onAudioActivated,
  onConsentGiven, // 🆕 Nuevo prop
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [, setIsActivatingAudio] = useState(false);

  const { getItem, setItem } = useCookieStorage();

  const {
    configure,
    state: { isReady: isLocutionActivated },
  } = useSpeechOutput();

  // Lógica para determinar visibilidad
  useEffect(() => {
    const hasConsent = getItem(STORAGE_KEYS.ANALYTICS_CONSENT);

    if (!hasConsent) {
      const timer = setTimeout(() => setIsVisible(true), 1500);
      return () => clearTimeout(timer);
    }
  }, [getItem]);

  // Función para guardar consentimiento
  const saveConsentPreferences = useCallback(
    (acceptAnalytics: boolean) => {
      const consentValue = acceptAnalytics
        ? CONSENT_VALUES.ACCEPTED
        : CONSENT_VALUES.REJECTED;

      setItem(STORAGE_KEYS.ANALYTICS_CONSENT, consentValue);
      setItem(STORAGE_KEYS.ANALYTICS_TIMESTAMP, new Date().toISOString());
    },
    [setItem]
  );

  const activateAudio = useCallback(async () => {
    if (!isLocutionActivated) {
      await configure("female");
    }

    setItem(STORAGE_KEYS.AUDIO_ACTIVATED, "true");

    onAudioActivated?.();
  }, [
    isLocutionActivated,
    configure,
    onAudioActivated,
    setItem,
  ]);

  const activateAudioWithConsent = useCallback(
    async (acceptAnalytics: boolean) => {
      setIsActivatingAudio(true);

      try {
        saveConsentPreferences(acceptAnalytics);

        if (acceptAnalytics) {
          // Solo activar audio si acepta las analíticas
          await activateAudio();
        } else {
          // Si rechaza, solo guardar preferencias y continuar
          onConsentGiven?.();
        }

        setIsVisible(false);
      } catch (error) {
        console.error("❌ Error procesando consentimiento:", error);
        setIsVisible(false);
      } finally {
        setIsActivatingAudio(false);
      }
    },
    [saveConsentPreferences, activateAudio, onConsentGiven]
  );

  const handleReject = useCallback(() => {
    activateAudioWithConsent(false);
  }, [activateAudioWithConsent]);

  const handleAccept = useCallback(() => {
    activateAudioWithConsent(true);
  }, [activateAudioWithConsent]);

  if (!isVisible) return null;

  return (
    <Modal
      title="Ayúdanos a mejorar"
      onClose={handleReject}
      onCancel={handleReject}
      onConfirm={handleAccept}
      cancelText="Cancelar"
      confirmText="Aceptar"
      body="Utilizamos cookies para ofrecerte un servicio más ágil y adaptado a tus preferencias."
    />
  );
};
