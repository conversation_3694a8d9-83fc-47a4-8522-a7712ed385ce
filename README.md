# 🎭 ENYGMA - <PERSON>ego de Adivinanzas con IA

## 📋 Descripción General

**Enygma** es un juego interactivo de adivinanzas donde el jugador y una IA compiten en un duelo mental. Inspirado en el clásico "20 preguntas", el objetivo es adivinar un personaje misterioso a través de preguntas estratégicas, utilizando tecnologías avanzadas de IA, síntesis de voz y reconocimiento de voz.

## 🎮 Modo de Juego

### **IA vs Jugador** (`ia_vs_player`)
- **Rol del jugador**: Hacer preguntas
- **Rol de la IA**: Responder preguntas
- **Flujo**: La IA genera un personaje secreto, el jugador hace preguntas para adivinarlo
- **Objetivo**: Adivinar el personaje que tiene la IA

## 🔄 Flujo del Juego

### Fase 1: Setup (Preparación)
```mermaid
graph TD
    A[Iniciar Juego] --> B[IA genera personaje]
    B --> C[Iniciar Partida]
```

### Fase 2: Questioning (Preguntas)
```mermaid
graph TD
    A[Fase de Preguntas] --> B{Rol del Jugador}
    B -->|Guesser| C[Usuario hace pregunta]
    B -->|Answerer| D[IA hace pregunta]
    C --> E[IA responde: Sí/No/Tal vez/No sé]
    D --> F[Usuario responde: Sí/No/Tal vez/No sé]
    E --> G{¿Límite alcanzado?}
    F --> G
    G -->|No| H[Continuar preguntando]
    G -->|Sí| I[Fase de Suposición]
    H --> B
```

### Fase 3: Guessing (Suposición Final)
```mermaid
graph TD
    A[Fase de Suposición] --> B[Realizar suposición final]
    B --> C{¿Correcto?}
    C -->|Sí| D[Victoria del adivinador]
    C -->|No| E[Victoria del oponente]
    D --> F[Fin del juego]
    E --> F
```

## 🎯 Reglas del Juego

### Límites
- **Máximo de preguntas**: 20 por partida
- **Tipos de respuesta válidos**: "Sí", "No", "Tal vez", "No sé"
- **Tiempo límite**: No hay límite de tiempo por pregunta
- **Suposiciones**: Una oportunidad final al alcanzar el límite de preguntas

### Estrategia
- Las preguntas deben ser estratégicas para obtener máxima información
- Las respuestas "Tal vez" y "No sé" son válidas cuando la información es ambigua
- La IA utiliza un sistema de confianza (0-100%) para evaluar sus respuestas

### Condiciones de Victoria
- **Victoria por adivinanza**: Adivinar correctamente el personaje
- **Victoria por agotamiento**: El oponente no logra adivinar en 20 preguntas
- **Empate**: Situaciones excepcionales donde no hay ganador claro

## 🏗️ Arquitectura Técnica

### Stack Tecnológico
- **Frontend**: React + TypeScript + Vite
- **Styling**: SCSS modular
- **IA**: Azure OpenAI / API personalizada
- **Síntesis de voz**: Azure Speech Services + Web Speech API
- **Reconocimiento de voz**: Web Speech API + servicios personalizados
- **Estado**: Context API + React Hooks

### Estructura de Contextos

#### 1. **AppContext**
- Gestión global de la aplicación
- Configuración y errores
- Navegación entre vistas
- Estado de inicialización

#### 2. **EnygmaGameContext**
- Lógica pura del juego
- Sesiones y mensajes
- Validaciones de entrada
- Progreso y estadísticas

#### 3. **GameOrchestratorContext**
- Coordinación de servicios
- Flujo de audio y speech
- Manejo de errores avanzado
- Sincronización de estados

#### 4. **SpeechInputContext**
- Reconocimiento de voz
- Transcripción en tiempo real
- Validación de respuestas
- Gestión de audio input

#### 5. **MHCContext**
- Integración con hardware específico
- Funcionalidades del dispositivo
- Gestión de conexiones
- APIs nativas

### Modelos de Datos

#### GameSession
```typescript
interface GameSession {
  id: string;
  mode: GameMode;
  phase: GamePhase;
  startTime: Date;
  endTime?: Date;
  questionCount: number;
  maxQuestions: number;
  currentCharacter?: string;
  aiConfidence: number;
  playerRole: PlayerRole;
  messages: GameMessage[];
  winner?: "ai" | "user" | "draw";
  finalGuess?: string;
  wasCorrect?: boolean;
}
```

#### GameMessage
```typescript
interface GameMessage {
  id: string;
  text: string;
  sender: "user" | "ai";
  timestamp: Date;
  type: "question" | "answer" | "guess" | "hint" | "system" | "presentation";
  confidence?: number;
  validatedResponse?: "yes" | "no" | "maybe" | "unknown";
  countsAsQuestion?: boolean;
}
```

## 🎨 Interfaz de Usuario

### Vistas Principales

#### 1. **WelcomeScreen**
- Pantalla de bienvenida inicial
- Animaciones de introducción
- Configuración inicial

#### 2. **MainView**
- Selección de modo de juego
- Acceso a reglas y configuración
- Interfaz principal de navegación

#### 3. **PlayView**
- Vista principal del juego
- Chat de preguntas y respuestas
- Controles de audio y micrófono
- Información de progreso

#### 4. **RulesView**
- Explicación detallada de las reglas
- Tutorial interactivo
- Ejemplos de estrategias

#### 5. **CluesView** & **LivesView**
- Pistas adicionales para el jugador
- Sistema de ayudas
- Estadísticas de progreso

### Componentes de Audio

#### Síntesis de Voz
- **Azure Speech Services**: Voz principal de alta calidad
- **Web Speech API**: Fallback para compatibilidad
- **Coordinador de Audio**: Gestiona colas y prioridades

#### Reconocimiento de Voz
- **Transcripción en tiempo real**: Convierte audio a texto
- **Validación automática**: Clasifica respuestas (sí/no/tal vez)
- **Normalización de texto**: Procesa acentos y variaciones

## 🔧 Configuración y Despliegue

### Variables de Entorno
```env
# APIs de IA
VITE_IA_API_URL=https://your-ai-api.com
VITE_IA_API_KEY=your-api-key

# Servicios de Speech
VITE_SPEECH_API_URL=https://your-speech-api.com
VITE_SPEECH_API_KEY=your-speech-key

# Configuración de Azure
VITE_AZURE_SPEECH_KEY=your-azure-key
VITE_AZURE_SPEECH_REGION=your-region

# Perplexity Service
VITE_PERPLEXITY_BASE_URL=https://your-perplexity-api.com/
VITE_PERPLEXITY_API_KEY=your-perplexity-api-key
VITE_PERPLEXITY_PRESETID=your-preset-id
```

### Scripts de Desarrollo
```bash
# Instalación
npm install

# Desarrollo
npm run dev

# Build de producción
npm run build

# Preview de producción
npm run preview

# Linting
npm run lint
```

### Despliegue
- **Plataforma**: Azure Static Web Apps
- **CI/CD**: GitHub Actions
- **Build**: Vite + TypeScript
- **Assets**: Optimización automática

## 📁 Estructura del Proyecto

```
src/
├── components/           # Componentes React reutilizables
│   ├── views/           # Vistas principales
│   ├── icons/           # Iconos SVG
│   └── Header/          # Componente de header
├── contexts/            # Context providers
├── models/              # Tipos TypeScript
├── services/            # Servicios de IA y audio
├── utils/               # Utilidades y helpers
├── styles/              # Estilos SCSS globales
└── assets/              # Recursos estáticos

public/
├── assets/              # Assets públicos
│   ├── game/           # Imágenes del juego
│   ├── sounds/         # Efectos de sonido
│   └── fonts/          # Fuentes personalizadas
├── game-modes.json     # Configuración de modos
└── game-rules.json     # Configuración de reglas
```

## 🚀 Funcionalidades Avanzadas

### Sistema de IA Inteligente
- **Generación automática** de personajes
- **Análisis de contexto** para respuestas coherentes
- **Sistema de confianza** que mejora con cada pregunta
- **Estrategias adaptativas** según el progreso del juego

### Audio Inmersivo
- **Síntesis de voz natural** con voces masculinas y femeninas
- **Reconocimiento de voz robusto** con múltiples idiomas
- **Efectos de sonido** contextuales
- **Música de fondo** adaptativa

### Experiencia de Usuario
- **Interfaz responsive** para móviles y desktop
- **Animaciones fluidas** y transiciones
- **Modo oscuro/claro** automático
- **Accesibilidad completa** con ARIA labels

### Analytics e Insights
- **Estadísticas de juego** detalladas
- **Análisis de patrones** de preguntas
- **Sugerencias estratégicas** en tiempo real
- **Historial de partidas** completo

---

*¡Disfruta del desafío mental con Enygma! 🧠✨*
